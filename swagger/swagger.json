{"swagger": "2.0", "info": {"description": "The LocalAI Rest API.", "title": "LocalAI API", "contact": {"name": "LocalAI", "url": "https://localai.io"}, "license": {"name": "MIT", "url": "https://raw.githubusercontent.com/mudler/LocalAI/master/LICENSE"}, "version": "2.0.0"}, "basePath": "/", "paths": {"/api/p2p": {"get": {"summary": "Returns available P2P nodes", "responses": {"200": {"description": "Response", "schema": {"type": "array", "items": {"$ref": "#/definitions/schema.P2PNodesResponse"}}}}}}, "/api/p2p/token": {"get": {"summary": "Show the P2P token", "responses": {"200": {"description": "Response", "schema": {"type": "string"}}}}}, "/backend/monitor": {"get": {"summary": "Backend monitor endpoint", "parameters": [{"description": "Backend statistics request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.BackendMonitorRequest"}}], "responses": {"200": {"description": "Response", "schema": {"$ref": "#/definitions/proto.StatusResponse"}}}}}, "/backend/shutdown": {"post": {"summary": "Backend monitor endpoint", "parameters": [{"description": "Backend statistics request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.BackendMonitorRequest"}}], "responses": {}}}, "/backends": {"get": {"summary": "List all Backends", "responses": {"200": {"description": "Response", "schema": {"type": "array", "items": {"$ref": "#/definitions/gallery.GalleryBackend"}}}}}}, "/backends/apply": {"post": {"summary": "Install backends to LocalAI.", "parameters": [{"description": "query params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/localai.GalleryBackend"}}], "responses": {"200": {"description": "Response", "schema": {"$ref": "#/definitions/schema.BackendResponse"}}}}}, "/backends/available": {"get": {"summary": "List all available Backends", "responses": {"200": {"description": "Response", "schema": {"type": "array", "items": {"$ref": "#/definitions/gallery.GalleryBackend"}}}}}}, "/backends/delete/{name}": {"post": {"summary": "delete backends from LocalAI.", "parameters": [{"type": "string", "description": "Backend name", "name": "name", "in": "path", "required": true}], "responses": {"200": {"description": "Response", "schema": {"$ref": "#/definitions/schema.BackendResponse"}}}}}, "/backends/galleries": {"get": {"summary": "List all Galleries", "responses": {"200": {"description": "Response", "schema": {"type": "array", "items": {"$ref": "#/definitions/config.Gallery"}}}}}}, "/backends/jobs": {"get": {"summary": "Returns all the jobs status progress", "responses": {"200": {"description": "Response", "schema": {"type": "object", "additionalProperties": {"$ref": "#/definitions/services.GalleryOpStatus"}}}}}}, "/backends/jobs/{uuid}": {"get": {"summary": "Returns the job status", "responses": {"200": {"description": "Response", "schema": {"$ref": "#/definitions/services.GalleryOpStatus"}}}}}, "/metrics": {"get": {"summary": "Prometheus metrics endpoint", "parameters": [{"description": "Gallery details", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/config.Gallery"}}], "responses": {}}}, "/models/apply": {"post": {"summary": "Install models to LocalAI.", "parameters": [{"description": "query params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/localai.GalleryModel"}}], "responses": {"200": {"description": "Response", "schema": {"$ref": "#/definitions/schema.GalleryResponse"}}}}}, "/models/available": {"get": {"summary": "List installable models.", "responses": {"200": {"description": "Response", "schema": {"type": "array", "items": {"$ref": "#/definitions/gallery.GalleryModel"}}}}}}, "/models/delete/{name}": {"post": {"summary": "delete models to LocalAI.", "parameters": [{"type": "string", "description": "Model name", "name": "name", "in": "path", "required": true}], "responses": {"200": {"description": "Response", "schema": {"$ref": "#/definitions/schema.GalleryResponse"}}}}}, "/models/galleries": {"get": {"summary": "List all Galleries", "responses": {"200": {"description": "Response", "schema": {"type": "array", "items": {"$ref": "#/definitions/config.Gallery"}}}}}}, "/models/jobs": {"get": {"summary": "Returns all the jobs status progress", "responses": {"200": {"description": "Response", "schema": {"type": "object", "additionalProperties": {"$ref": "#/definitions/services.GalleryOpStatus"}}}}}}, "/models/jobs/{uuid}": {"get": {"summary": "Returns the job status", "responses": {"200": {"description": "Response", "schema": {"$ref": "#/definitions/services.GalleryOpStatus"}}}}}, "/system": {"get": {"summary": "Show the LocalAI instance information", "responses": {"200": {"description": "Response", "schema": {"$ref": "#/definitions/schema.SystemInformationResponse"}}}}}, "/tokenMetrics": {"get": {"consumes": ["application/json"], "produces": ["audio/x-wav"], "summary": "Get TokenMetrics for Active Slot.", "responses": {"200": {"description": "generated audio/wav file", "schema": {"type": "string"}}}}}, "/tts": {"post": {"consumes": ["application/json"], "produces": ["audio/x-wav"], "summary": "Generates audio from the input text.", "parameters": [{"description": "query params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.TTSRequest"}}], "responses": {"200": {"description": "generated audio/wav file", "schema": {"type": "string"}}}}}, "/v1/audio/speech": {"post": {"consumes": ["application/json"], "produces": ["audio/x-wav"], "summary": "Generates audio from the input text.", "parameters": [{"description": "query params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.TTSRequest"}}], "responses": {"200": {"description": "generated audio/wav file", "schema": {"type": "string"}}}}}, "/v1/audio/transcriptions": {"post": {"consumes": ["multipart/form-data"], "summary": "Transcribes audio into the input language.", "parameters": [{"type": "string", "description": "model", "name": "model", "in": "formData", "required": true}, {"type": "file", "description": "file", "name": "file", "in": "formData", "required": true}], "responses": {"200": {"description": "Response", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/v1/chat/completions": {"post": {"summary": "Generate a chat completions for a given prompt and model.", "parameters": [{"description": "query params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.OpenAIRequest"}}], "responses": {"200": {"description": "Response", "schema": {"$ref": "#/definitions/schema.OpenAIResponse"}}}}}, "/v1/completions": {"post": {"summary": "Generate completions for a given prompt and model.", "parameters": [{"description": "query params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.OpenAIRequest"}}], "responses": {"200": {"description": "Response", "schema": {"$ref": "#/definitions/schema.OpenAIResponse"}}}}}, "/v1/detection": {"post": {"summary": "Detects objects in the input image.", "parameters": [{"description": "query params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.DetectionRequest"}}], "responses": {"200": {"description": "Response", "schema": {"$ref": "#/definitions/schema.DetectionResponse"}}}}}, "/v1/edits": {"post": {"summary": "OpenAI edit endpoint", "parameters": [{"description": "query params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.OpenAIRequest"}}], "responses": {"200": {"description": "Response", "schema": {"$ref": "#/definitions/schema.OpenAIResponse"}}}}}, "/v1/embeddings": {"post": {"summary": "Get a vector representation of a given input that can be easily consumed by machine learning models and algorithms.", "parameters": [{"description": "query params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.OpenAIRequest"}}], "responses": {"200": {"description": "Response", "schema": {"$ref": "#/definitions/schema.OpenAIResponse"}}}}}, "/v1/images/generations": {"post": {"summary": "Creates an image given a prompt.", "parameters": [{"description": "query params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.OpenAIRequest"}}], "responses": {"200": {"description": "Response", "schema": {"$ref": "#/definitions/schema.OpenAIResponse"}}}}}, "/v1/models": {"get": {"summary": "List and describe the various models available in the API.", "responses": {"200": {"description": "Response", "schema": {"$ref": "#/definitions/schema.ModelsDataResponse"}}}}}, "/v1/rerank": {"post": {"summary": "Reranks a list of phrases by relevance to a given text query.", "parameters": [{"description": "query params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.JINARerankRequest"}}], "responses": {"200": {"description": "Response", "schema": {"$ref": "#/definitions/schema.JINARerankResponse"}}}}}, "/v1/sound-generation": {"post": {"summary": "Generates audio from the input text.", "parameters": [{"description": "query params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.ElevenLabsSoundGenerationRequest"}}], "responses": {"200": {"description": "Response", "schema": {"type": "string"}}}}}, "/v1/text-to-speech/{voice-id}": {"post": {"summary": "Generates audio from the input text.", "parameters": [{"type": "string", "description": "Account ID", "name": "voice-id", "in": "path", "required": true}, {"description": "query params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.TTSRequest"}}], "responses": {"200": {"description": "Response", "schema": {"type": "string"}}}}}, "/v1/tokenMetrics": {"get": {"consumes": ["application/json"], "produces": ["audio/x-wav"], "summary": "Get TokenMetrics for Active Slot.", "responses": {"200": {"description": "generated audio/wav file", "schema": {"type": "string"}}}}}, "/v1/tokenize": {"post": {"summary": "Tokenize the input.", "parameters": [{"description": "Request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.TokenizeRequest"}}], "responses": {"200": {"description": "Response", "schema": {"$ref": "#/definitions/schema.TokenizeResponse"}}}}}, "/vad": {"post": {"consumes": ["application/json"], "summary": "Detect voice fragments in an audio stream", "parameters": [{"description": "query params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.VADRequest"}}], "responses": {"200": {"description": "Response", "schema": {"$ref": "#/definitions/proto.VADResponse"}}}}}, "/video": {"post": {"summary": "Creates a video given a prompt.", "parameters": [{"description": "query params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.OpenAIRequest"}}], "responses": {"200": {"description": "Response", "schema": {"$ref": "#/definitions/schema.OpenAIResponse"}}}}}}, "definitions": {"config.Gallery": {"type": "object", "properties": {"name": {"type": "string"}, "url": {"type": "string"}}}, "functions.Function": {"type": "object", "properties": {"description": {"type": "string"}, "name": {"type": "string"}, "parameters": {"type": "object", "additionalProperties": true}, "strict": {"type": "boolean"}}}, "functions.Item": {"type": "object", "properties": {"properties": {"type": "object", "additionalProperties": true}, "type": {"type": "string"}}}, "functions.JSONFunctionStructure": {"type": "object", "properties": {"$defs": {"type": "object", "additionalProperties": true}, "anyOf": {"type": "array", "items": {"$ref": "#/definitions/functions.Item"}}, "oneOf": {"type": "array", "items": {"$ref": "#/definitions/functions.Item"}}}}, "functions.Tool": {"type": "object", "properties": {"function": {"$ref": "#/definitions/functions.Function"}, "type": {"type": "string"}}}, "gallery.File": {"type": "object", "properties": {"filename": {"type": "string"}, "sha256": {"type": "string"}, "uri": {"type": "string"}}}, "gallery.GalleryBackend": {"type": "object", "properties": {"alias": {"type": "string"}, "capabilities": {"type": "object", "additionalProperties": {"type": "string"}}, "description": {"type": "string"}, "files": {"description": "AdditionalFiles are used to add additional files to the model", "type": "array", "items": {"$ref": "#/definitions/gallery.File"}}, "gallery": {"description": "Gallery is a reference to the gallery which contains the model", "allOf": [{"$ref": "#/definitions/config.Gallery"}]}, "icon": {"type": "string"}, "installed": {"description": "Installed is used to indicate if the model is installed or not", "type": "boolean"}, "license": {"type": "string"}, "mirrors": {"type": "array", "items": {"type": "string"}}, "name": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}, "uri": {"type": "string"}, "url": {"type": "string"}, "urls": {"type": "array", "items": {"type": "string"}}}}, "gallery.GalleryModel": {"type": "object", "properties": {"config_file": {"description": "config_file is read in the situation where URL is blank - and therefore this is a base config.", "type": "object", "additionalProperties": true}, "description": {"type": "string"}, "files": {"description": "AdditionalFiles are used to add additional files to the model", "type": "array", "items": {"$ref": "#/definitions/gallery.File"}}, "gallery": {"description": "Gallery is a reference to the gallery which contains the model", "allOf": [{"$ref": "#/definitions/config.Gallery"}]}, "icon": {"type": "string"}, "installed": {"description": "Installed is used to indicate if the model is installed or not", "type": "boolean"}, "license": {"type": "string"}, "name": {"type": "string"}, "overrides": {"description": "Overrides are used to override the configuration of the model located at URL", "type": "object", "additionalProperties": true}, "tags": {"type": "array", "items": {"type": "string"}}, "url": {"type": "string"}, "urls": {"type": "array", "items": {"type": "string"}}}}, "localai.GalleryBackend": {"type": "object", "properties": {"id": {"type": "string"}}}, "localai.GalleryModel": {"type": "object", "properties": {"config_file": {"description": "config_file is read in the situation where URL is blank - and therefore this is a base config.", "type": "object", "additionalProperties": true}, "description": {"type": "string"}, "files": {"description": "AdditionalFiles are used to add additional files to the model", "type": "array", "items": {"$ref": "#/definitions/gallery.File"}}, "gallery": {"description": "Gallery is a reference to the gallery which contains the model", "allOf": [{"$ref": "#/definitions/config.Gallery"}]}, "icon": {"type": "string"}, "id": {"type": "string"}, "installed": {"description": "Installed is used to indicate if the model is installed or not", "type": "boolean"}, "license": {"type": "string"}, "name": {"type": "string"}, "overrides": {"description": "Overrides are used to override the configuration of the model located at URL", "type": "object", "additionalProperties": true}, "tags": {"type": "array", "items": {"type": "string"}}, "url": {"type": "string"}, "urls": {"type": "array", "items": {"type": "string"}}}}, "p2p.NodeData": {"type": "object", "properties": {"id": {"type": "string"}, "lastSeen": {"type": "string"}, "name": {"type": "string"}, "serviceID": {"type": "string"}, "tunnelAddress": {"type": "string"}}}, "proto.MemoryUsageData": {"type": "object", "properties": {"breakdown": {"type": "object", "additionalProperties": {"type": "integer", "format": "int64"}}, "total": {"type": "integer"}}}, "proto.StatusResponse": {"type": "object", "properties": {"memory": {"$ref": "#/definitions/proto.MemoryUsageData"}, "state": {"$ref": "#/definitions/proto.StatusResponse_State"}}}, "proto.StatusResponse_State": {"type": "integer", "format": "int32", "enum": [0, 1, 2, -1], "x-enum-varnames": ["StatusResponse_UNINITIALIZED", "StatusResponse_BUSY", "StatusResponse_READY", "StatusResponse_ERROR"]}, "proto.VADResponse": {"type": "object", "properties": {"segments": {"type": "array", "items": {"$ref": "#/definitions/proto.VADSegment"}}}}, "proto.VADSegment": {"type": "object", "properties": {"end": {"type": "number"}, "start": {"type": "number"}}}, "schema.BackendMonitorRequest": {"type": "object", "properties": {"model": {"type": "string"}}}, "schema.BackendResponse": {"type": "object", "properties": {"id": {"type": "string"}, "status_url": {"type": "string"}}}, "schema.Choice": {"type": "object", "properties": {"delta": {"$ref": "#/definitions/schema.Message"}, "finish_reason": {"type": "string"}, "index": {"type": "integer"}, "message": {"$ref": "#/definitions/schema.Message"}, "text": {"type": "string"}}}, "schema.Detection": {"type": "object", "properties": {"class_name": {"type": "string"}, "height": {"type": "number"}, "width": {"type": "number"}, "x": {"type": "number"}, "y": {"type": "number"}}}, "schema.DetectionRequest": {"type": "object", "properties": {"image": {"type": "string"}, "model": {"type": "string"}}}, "schema.DetectionResponse": {"type": "object", "properties": {"detections": {"type": "array", "items": {"$ref": "#/definitions/schema.Detection"}}}}, "schema.ElevenLabsSoundGenerationRequest": {"type": "object", "properties": {"do_sample": {"type": "boolean"}, "duration_seconds": {"type": "number"}, "model_id": {"type": "string"}, "prompt_influence": {"type": "number"}, "text": {"type": "string"}}}, "schema.FunctionCall": {"type": "object", "properties": {"arguments": {"type": "string"}, "name": {"type": "string"}}}, "schema.GalleryResponse": {"type": "object", "properties": {"status": {"type": "string"}, "uuid": {"type": "string"}}}, "schema.Item": {"type": "object", "properties": {"b64_json": {"type": "string"}, "embedding": {"type": "array", "items": {"type": "number"}}, "index": {"type": "integer"}, "object": {"type": "string"}, "url": {"description": "Images", "type": "string"}}}, "schema.JINADocumentResult": {"type": "object", "properties": {"document": {"$ref": "#/definitions/schema.JINAText"}, "index": {"type": "integer"}, "relevance_score": {"type": "number"}}}, "schema.JINARerankRequest": {"type": "object", "properties": {"backend": {"type": "string"}, "documents": {"type": "array", "items": {"type": "string"}}, "model": {"type": "string"}, "query": {"type": "string"}, "top_n": {"type": "integer"}}}, "schema.JINARerankResponse": {"type": "object", "properties": {"model": {"type": "string"}, "results": {"type": "array", "items": {"$ref": "#/definitions/schema.JINADocumentResult"}}, "usage": {"$ref": "#/definitions/schema.JINAUsageInfo"}}}, "schema.JINAText": {"type": "object", "properties": {"text": {"type": "string"}}}, "schema.JINAUsageInfo": {"type": "object", "properties": {"prompt_tokens": {"type": "integer"}, "total_tokens": {"type": "integer"}}}, "schema.Message": {"type": "object", "properties": {"content": {"description": "The message content"}, "function_call": {"description": "A result of a function call"}, "name": {"description": "The message name (used for tools calls)", "type": "string"}, "role": {"description": "The message role", "type": "string"}, "string_audios": {"type": "array", "items": {"type": "string"}}, "string_content": {"type": "string"}, "string_images": {"type": "array", "items": {"type": "string"}}, "string_videos": {"type": "array", "items": {"type": "string"}}, "tool_calls": {"type": "array", "items": {"$ref": "#/definitions/schema.ToolCall"}}}}, "schema.ModelsDataResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/schema.OpenAIModel"}}, "object": {"type": "string"}}}, "schema.OpenAIModel": {"type": "object", "properties": {"id": {"type": "string"}, "object": {"type": "string"}}}, "schema.OpenAIRequest": {"type": "object", "required": ["file"], "properties": {"backend": {"type": "string"}, "batch": {"description": "Custom parameters - not present in the OpenAI API", "type": "integer"}, "clip_skip": {"description": "Diffusers", "type": "integer"}, "echo": {"type": "boolean"}, "file": {"description": "whisper", "type": "string"}, "files": {"description": "Multiple input images for img2img or inpainting", "type": "array", "items": {"type": "string"}}, "frequency_penalty": {"type": "number"}, "function_call": {"description": "might be a string or an object"}, "functions": {"description": "A list of available functions to call", "type": "array", "items": {"$ref": "#/definitions/functions.Function"}}, "grammar": {"description": "A grammar to constrain the LLM output", "type": "string"}, "grammar_json_functions": {"$ref": "#/definitions/functions.JSONFunctionStructure"}, "ignore_eos": {"type": "boolean"}, "input": {}, "instruction": {"description": "Edit endpoint", "type": "string"}, "language": {"description": "Also part of the OpenAI official spec", "type": "string"}, "max_tokens": {"type": "integer"}, "messages": {"description": "Messages is read only by chat/completion API calls", "type": "array", "items": {"$ref": "#/definitions/schema.Message"}}, "mode": {"description": "Image (not supported by OpenAI)", "type": "integer"}, "model": {"type": "string"}, "model_base_name": {"type": "string"}, "n": {"description": "Also part of the OpenAI official spec. use it for returning multiple results", "type": "integer"}, "n_keep": {"type": "integer"}, "negative_prompt": {"type": "string"}, "negative_prompt_scale": {"type": "number"}, "presence_penalty": {"type": "number"}, "prompt": {"description": "Prompt is read only by completion/image API calls"}, "quality": {"type": "string"}, "ref_images": {"description": "Reference images for models that support them (e.g., Flux Kontext)", "type": "array", "items": {"type": "string"}}, "repeat_last_n": {"type": "integer"}, "repeat_penalty": {"type": "number"}, "response_format": {"description": "whisper/image"}, "rope_freq_base": {"type": "number"}, "rope_freq_scale": {"type": "number"}, "seed": {"type": "integer"}, "size": {"description": "image", "type": "string"}, "step": {"type": "integer"}, "stop": {}, "stream": {"type": "boolean"}, "temperature": {"type": "number"}, "tfz": {"type": "number"}, "tokenizer": {"description": "RWKV (?)", "type": "string"}, "tool_choice": {}, "tools": {"type": "array", "items": {"$ref": "#/definitions/functions.Tool"}}, "top_k": {"type": "integer"}, "top_p": {"description": "Common options between all the API calls, part of the OpenAI spec", "type": "number"}, "translate": {"description": "Only for audio transcription", "type": "boolean"}, "typical_p": {"type": "number"}}}, "schema.OpenAIResponse": {"type": "object", "properties": {"choices": {"type": "array", "items": {"$ref": "#/definitions/schema.Choice"}}, "created": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/schema.Item"}}, "id": {"type": "string"}, "model": {"type": "string"}, "object": {"type": "string"}, "usage": {"$ref": "#/definitions/schema.OpenAIUsage"}}}, "schema.OpenAIUsage": {"type": "object", "properties": {"completion_tokens": {"type": "integer"}, "prompt_tokens": {"type": "integer"}, "timing_prompt_processing": {"description": "Extra timing data, disabled by default as is't not a part of OpenAI specification", "type": "number"}, "timing_token_generation": {"type": "number"}, "total_tokens": {"type": "integer"}}}, "schema.P2PNodesResponse": {"type": "object", "properties": {"federated_nodes": {"type": "array", "items": {"$ref": "#/definitions/p2p.NodeData"}}, "nodes": {"type": "array", "items": {"$ref": "#/definitions/p2p.NodeData"}}}}, "schema.SysInfoModel": {"type": "object", "properties": {"id": {"type": "string"}}}, "schema.SystemInformationResponse": {"type": "object", "properties": {"backends": {"type": "array", "items": {"type": "string"}}, "loaded_models": {"type": "array", "items": {"$ref": "#/definitions/schema.SysInfoModel"}}}}, "schema.TTSRequest": {"description": "TTS request body", "type": "object", "properties": {"backend": {"type": "string"}, "input": {"description": "text input", "type": "string"}, "language": {"description": "(optional) language to use with TTS model", "type": "string"}, "model": {"type": "string"}, "response_format": {"description": "(optional) output format", "type": "string"}, "voice": {"description": "voice audio file or speaker id", "type": "string"}}}, "schema.TokenizeRequest": {"type": "object", "properties": {"content": {"type": "string"}, "model": {"type": "string"}}}, "schema.TokenizeResponse": {"type": "object", "properties": {"tokens": {"type": "array", "items": {"type": "integer"}}}}, "schema.ToolCall": {"type": "object", "properties": {"function": {"$ref": "#/definitions/schema.FunctionCall"}, "id": {"type": "string"}, "index": {"type": "integer"}, "type": {"type": "string"}}}, "schema.VADRequest": {"description": "VAD request body", "type": "object", "properties": {"audio": {"description": "model name or full path", "type": "array", "items": {"type": "number"}}, "model": {"type": "string"}}}, "services.GalleryOpStatus": {"type": "object", "properties": {"deletion": {"description": "Deletion is true if the operation is a deletion", "type": "boolean"}, "downloaded_size": {"type": "string"}, "error": {}, "file_name": {"type": "string"}, "file_size": {"type": "string"}, "gallery_element_name": {"type": "string"}, "message": {"type": "string"}, "processed": {"type": "boolean"}, "progress": {"type": "number"}}}}, "securityDefinitions": {"BearerAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}
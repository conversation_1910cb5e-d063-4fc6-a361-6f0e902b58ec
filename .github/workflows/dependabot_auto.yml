name: Dependabot auto-merge
on:
- pull_request_target

permissions:
  contents: write
  pull-requests: write
  packages: read

jobs:
  dependabot:
    runs-on: ubuntu-latest
    if: ${{ github.actor == 'dependabot[bot]' }}
    steps:
      - name: Dependabot metadata
        id: metadata
        uses: dependabot/fetch-metadata@v2.4.0
        with:
          github-token: "${{ secrets.GITHUB_TOKEN }}"
          skip-commit-verification: true

      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Approve a PR if not already approved
        run: |
          gh pr checkout "$PR_URL"
            if [ "$(gh pr status --json reviewDecision -q .currentBranch.reviewDecision)" != "APPROVED" ];
          then
            gh pr review --approve "$PR_URL"
          else
            echo "PR already approved.";
          fi
        env:
          PR_URL: ${{github.event.pull_request.html_url}}
          GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}

      - name: Enable auto-merge for Dependabot PRs
        if: ${{ contains(github.event.pull_request.title, 'bump')}}
        run: gh pr merge --auto --squash "$PR_URL"
        env:
          PR_URL: ${{github.event.pull_request.html_url}}
          GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}

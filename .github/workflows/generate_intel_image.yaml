name: 'generate and publish intel docker caches'

on:
  workflow_dispatch:
  push:
    branches:
      - master

concurrency:
  group: intel-cache-${{ github.head_ref || github.ref }}-${{ github.repository }}
  cancel-in-progress: true

jobs:
  generate_caches:
    strategy:
      matrix:
        include:
          - base-image: intel/oneapi-basekit:2025.2.0-0-devel-ubuntu22.04
            runs-on: 'ubuntu-latest'
            platforms: 'linux/amd64'
    runs-on: ${{matrix.runs-on}}
    steps:
      - name: Set up QEMU
        uses: docker/setup-qemu-action@master
        with:
          platforms: all
      - name: Login to DockerHub
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PASSWORD }}

      - name: Login to quay
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          registry: quay.io
          username: ${{ secrets.LOCALAI_REGISTRY_USERNAME }}
          password: ${{ secrets.LOCALAI_REGISTRY_PASSWORD }}
      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@master

      - name: Checkout
        uses: actions/checkout@v4

      - name: Cache Intel images
        uses: docker/build-push-action@v6
        with:
          builder: ${{ steps.buildx.outputs.name }}
          build-args: |
            BASE_IMAGE=${{ matrix.base-image }}
          context: .
          file: ./Dockerfile
          tags: quay.io/go-skynet/intel-oneapi-base:latest
          push: true
          target: intel
          platforms: ${{ matrix.platforms }}

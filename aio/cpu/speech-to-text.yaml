name: whisper-1
backend: whisper
parameters:
  model: ggml-whisper-base.bin

usage: |
    ## example audio file
    wget --quiet --show-progress -O gb1.ogg https://upload.wikimedia.org/wikipedia/commons/1/1f/<PERSON>_<PERSON>_Bush_Columbia_FINAL.ogg

    ## Send the example audio file to the transcriptions endpoint
    curl http://localhost:8080/v1/audio/transcriptions \
         -H "Content-Type: multipart/form-data" \
         -F file="@$PWD/gb1.ogg" -F model="whisper-1"

download_files:
- filename: "ggml-whisper-base.bin"
  sha256: "60ed5bc3dd14eea856493d334349b405782ddcaf0028d4b5df4088345fba2efe"
  uri: "https://huggingface.co/ggerganov/whisper.cpp/resolve/main/ggml-base.bin"
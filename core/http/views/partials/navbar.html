<nav class="bg-gradient-to-r from-gray-900 to-gray-950 shadow-lg border-b border-gray-800/50">
    <div class="container mx-auto px-4 py-3">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <!-- Logo Image -->
                <a href="./" class="flex items-center group">
                    <img src="static/logo_horizontal.png" 
                         alt="LocalAI Logo" 
                         class="h-14 mr-3 brightness-110 transition-all duration-300 group-hover:brightness-125">
                </a>
            </div>
            
            <!-- Menu button for small screens -->
            <div class="lg:hidden">
                <button id="menu-toggle" class="text-gray-300 hover:text-blue-400 focus:outline-none p-2 rounded-lg transition duration-300 ease-in-out hover:bg-gray-800/70">
                    <i class="fas fa-bars fa-lg"></i>
                </button>
            </div>
            
            <!-- Navigation links -->
            <div class="hidden lg:flex lg:items-center lg:justify-end lg:space-x-1">
                <a href="./" class="text-gray-300 hover:text-white px-3 py-2 rounded-lg transition duration-300 ease-in-out hover:bg-blue-900/30 flex items-center">
                    <i class="fas fa-home text-blue-400 mr-2"></i>Home
                </a>
                <a href="browse/" class="text-gray-300 hover:text-white px-3 py-2 rounded-lg transition duration-300 ease-in-out hover:bg-blue-900/30 flex items-center">
                    <i class="fas fa-brain text-blue-400 mr-2"></i>Models
                </a>
                <a href="browse/backends" class="text-gray-300 hover:text-white px-3 py-2 rounded-lg transition duration-300 ease-in-out hover:bg-blue-900/30 flex items-center">
                    <i class="fas fa-server text-blue-400 mr-2"></i>Backends
                </a>
                <a href="chat/" class="text-gray-300 hover:text-white px-3 py-2 rounded-lg transition duration-300 ease-in-out hover:bg-blue-900/30 flex items-center">
                    <i class="fa-solid fa-comments text-blue-400 mr-2"></i>Chat
                </a>
                <a href="text2image/" class="text-gray-300 hover:text-white px-3 py-2 rounded-lg transition duration-300 ease-in-out hover:bg-blue-900/30 flex items-center">
                    <i class="fas fa-image text-blue-400 mr-2"></i>Generate images
                </a>
                <a href="tts/" class="text-gray-300 hover:text-white px-3 py-2 rounded-lg transition duration-300 ease-in-out hover:bg-blue-900/30 flex items-center">
                    <i class="fa-solid fa-music text-blue-400 mr-2"></i>TTS
                </a>
                <a href="talk/" class="text-gray-300 hover:text-white px-3 py-2 rounded-lg transition duration-300 ease-in-out hover:bg-blue-900/30 flex items-center">
                    <i class="fa-solid fa-phone text-blue-400 mr-2"></i>Talk
                </a>
                <a href="p2p/" class="text-gray-300 hover:text-white px-3 py-2 rounded-lg transition duration-300 ease-in-out hover:bg-blue-900/30 flex items-center">
                    <i class="fa-solid fa-circle-nodes text-blue-400 mr-2"></i>Swarm
                </a>
                <a href="swagger/" class="text-gray-300 hover:text-white px-3 py-2 rounded-lg transition duration-300 ease-in-out hover:bg-blue-900/30 flex items-center">
                    <i class="fas fa-code text-blue-400 mr-2"></i>API
                </a>
            </div>
        </div>
        
        <!-- Collapsible menu for small screens -->
        <div class="hidden lg:hidden" id="mobile-menu">
            <div class="pt-3 pb-2 space-y-1 border-t border-gray-800/50 mt-2">
                <a href="./" class="block text-gray-300 hover:text-white hover:bg-blue-900/30 px-3 py-2 rounded-lg transition duration-300 ease-in-out flex items-center">
                    <i class="fas fa-home text-blue-400 mr-3 w-5 text-center"></i>Home
                </a>
                <a href="browse/" class="block text-gray-300 hover:text-white hover:bg-blue-900/30 px-3 py-2 rounded-lg transition duration-300 ease-in-out flex items-center">
                    <i class="fas fa-brain text-blue-400 mr-3 w-5 text-center"></i>Models
                </a>
                <a href="browse/backends" class="block text-gray-300 hover:text-white hover:bg-blue-900/30 px-3 py-2 rounded-lg transition duration-300 ease-in-out flex items-center">
                    <i class="fas fa-server text-blue-400 mr-3 w-5 text-center"></i>Backends
                </a>
                <a href="chat/" class="block text-gray-300 hover:text-white hover:bg-blue-900/30 px-3 py-2 rounded-lg transition duration-300 ease-in-out flex items-center">
                    <i class="fa-solid fa-comments text-blue-400 mr-3 w-5 text-center"></i>Chat
                </a>
                <a href="text2image/" class="block text-gray-300 hover:text-white hover:bg-blue-900/30 px-3 py-2 rounded-lg transition duration-300 ease-in-out flex items-center">
                    <i class="fas fa-image text-blue-400 mr-3 w-5 text-center"></i>Generate images
                </a>
                <a href="tts/" class="block text-gray-300 hover:text-white hover:bg-blue-900/30 px-3 py-2 rounded-lg transition duration-300 ease-in-out flex items-center">
                    <i class="fa-solid fa-music text-blue-400 mr-3 w-5 text-center"></i>TTS
                </a>
                <a href="talk/" class="block text-gray-300 hover:text-white hover:bg-blue-900/30 px-3 py-2 rounded-lg transition duration-300 ease-in-out flex items-center">
                    <i class="fa-solid fa-phone text-blue-400 mr-3 w-5 text-center"></i>Talk
                </a>
                <a href="p2p/" class="block text-gray-300 hover:text-white hover:bg-blue-900/30 px-3 py-2 rounded-lg transition duration-300 ease-in-out flex items-center">
                    <i class="fa-solid fa-circle-nodes text-blue-400 mr-3 w-5 text-center"></i>Swarm
                </a>
                <a href="swagger/" class="block text-gray-300 hover:text-white hover:bg-blue-900/30 px-3 py-2 rounded-lg transition duration-300 ease-in-out flex items-center">
                    <i class="fas fa-code text-blue-400 mr-3 w-5 text-center"></i>API
                </a>
            </div>
        </div>
    </div>
</nav>

<script>
    // JavaScript to toggle the mobile menu with animation
    document.getElementById('menu-toggle').addEventListener('click', function () {
        var mobileMenu = document.getElementById('mobile-menu');
        if (mobileMenu.classList.contains('hidden')) {
            mobileMenu.classList.remove('hidden');
            // Use setTimeout to create a mild animation effect
            setTimeout(function() {
                mobileMenu.classList.add('opacity-100');
                mobileMenu.classList.remove('opacity-0');
            }, 10);
        } else {
            mobileMenu.classList.add('opacity-0');
            mobileMenu.classList.remove('opacity-100');
            // Wait for transition to finish before hiding
            setTimeout(function() {
                mobileMenu.classList.add('hidden');
            }, 300);
        }
    });
</script>
<!DOCTYPE html>
<html lang="en">
{{template "views/partials/head" .}}
<script defer src="static/tts.js"></script>

<body class="bg-gradient-to-br from-gray-900 to-gray-950 text-gray-200">
<div class="flex flex-col min-h-screen">
   
    {{template "views/partials/navbar" .}}
    <div class="container mx-auto px-4 py-8 flex-grow">
        <!-- Hero Section -->
        <div class="bg-gradient-to-r from-purple-900/30 to-indigo-900/30 rounded-2xl shadow-xl p-8 mb-10">
            <div class="max-w-4xl mx-auto text-center">
                <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">
                    <span class="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-indigo-400">
                        <i class="fas fa-volume-high mr-2"></i>Text to Speech {{ if .Model }} with {{.Model}} {{ end }}
                    </span>
                </h1>
                <p class="text-xl text-gray-300 mb-6">Convert your text into natural-sounding speech</p>
                <div class="flex flex-wrap justify-center gap-4">
                    <a href="https://localai.io/features/text-to-audio/" target="_blank" 
                        class="group flex items-center bg-blue-600 hover:bg-blue-700 text-white py-2 px-6 rounded-lg transition duration-300 ease-in-out transform hover:scale-105 hover:shadow-lg">
                        <i class="fas fa-book-reader mr-2"></i>
                        <span>Documentation</span>
                        <i class="fas fa-arrow-right opacity-0 group-hover:opacity-100 group-hover:translate-x-2 ml-2 transition-all duration-300"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- TTS Interface -->
        <div class="max-w-3xl mx-auto">
            <div class="bg-gray-800/90 border border-gray-700/50 rounded-xl overflow-hidden transition-all duration-300 shadow-lg shadow-blue-900/20">
                <!-- Header with Model Selection -->
                <div class="border-b border-gray-700 p-5">
                    <div class="flex flex-col sm:flex-row items-center justify-between gap-4">
                        <!-- Model Selection -->
                        <div class="flex items-center">
                            <label for="model-select" class="mr-3 text-gray-300 font-medium">
                                <i class="fas fa-microphone-lines text-purple-400 mr-2"></i>Model:
                            </label>
                            <select 
                                id="model-select"
                                x-data="{ link : '' }" 
                                x-model="link" 
                                x-init="$watch('link', value => window.location = link)" 
                                class="bg-gray-800 text-white border border-gray-700 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-30 rounded-lg shadow-sm p-2.5 appearance-none"
                            >	
                                <option value="" disabled class="text-gray-400">Select a model</option>
                                {{ $model:=.Model}}
                                {{ range .ModelsConfig }}
                                    {{ $cfg := . }}
                                    {{ range .KnownUsecaseStrings }}
                                        {{ if eq . "FLAG_TTS" }}
                                            <option value="tts/{{$cfg.Name}}" {{ if eq $cfg.Name $model }} selected {{end}} class="bg-gray-700 text-white">{{$cfg.Name}}</option>
                                        {{ end }}
                                    {{ end }}
                                {{ end }}
                                {{ range .ModelsWithoutConfig }}
                                    <option value="tts/{{.}}" {{ if eq . $model }} selected {{ end }} class="bg-gray-700 text-white">{{.}}</option>
                                {{end}}
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Input Area -->
                <div class="p-6">
                    <div class="bg-blue-900/20 border border-blue-700/50 rounded-lg p-4 mb-6">
                        <div class="flex items-start">
                            <i class="fas fa-info-circle text-blue-400 mt-1 mr-3 flex-shrink-0"></i>
                            <p class="text-gray-300">
                                Enter your text below and submit to generate speech with the selected TTS model.
                                The generated audio will appear below the input field.
                            </p>
                        </div>
                    </div>

                    <input id="tts-model" type="hidden" value="{{.Model}}">
                    <form id="tts" action="tts/{{.Model}}" method="get" class="mb-6">
                        <div class="relative">
                            <input
                                type="text"
                                id="input"
                                name="input"
                                placeholder="Enter text to convert to speech..."
                                autocomplete="off"
                                class="w-full bg-gray-800 text-white border border-gray-700 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-30 rounded-lg shadow-sm p-4 pl-4 pr-12"
                                required
                            />
                            <button type="submit" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-400 hover:text-blue-300 transition">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </form>

                    <!-- Loading indicator -->
                    <div class="flex justify-center my-6">
                        <div id="loader" class="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-purple-500" style="display: none;"></div>
                    </div>

                    <!-- Results Area -->
                    <div class="bg-gray-700/50 border border-gray-600/50 rounded-lg p-4 min-h-[100px] flex items-center justify-center">
                        <div id="result" class="w-full"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{template "views/partials/footer" .}}
</div>
</body>
</html>
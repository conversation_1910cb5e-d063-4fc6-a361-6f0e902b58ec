---
name: "deepseek-r1"

config_file: |
  backend: "llama-cpp"
  context_size: 131072
  mmap: true
  f16: true
  stopwords:
    - <｜begin▁of▁sentence｜>
    - <｜end▁of▁sentence｜>
    - <｜User｜>
    - <｜Assistant｜>
  template:
    chat_message: |
      {{if eq .<PERSON><PERSON><PERSON> "system" -}}{{.Content }}
      {{ end -}}
      {{if eq .<PERSON><PERSON><PERSON> "user" -}}<｜User｜>{{.Content}}
      {{end -}}
      {{if eq .<PERSON><PERSON><PERSON> "assistant" -}}<｜Assistant｜>{{.Content}}<｜end▁of▁sentence｜>{{end}}
    completion: |
      {{.Input}}
    chat: |
      {{.Input -}}<｜Assistant｜>

---
name: "deephermes"

config_file: |
  backend: "llama-cpp"
  mmap: true
  context_size: 8192
  stopwords:
  - "<|im_end|>"
  - "<dummy32000>"
  - "<|eot_id|>"
  - "<|end_of_text|>"
  function:
    disable_no_action: true
    grammar:
      triggers:
      - word: "<tool_call>"
        at_start: false
  template:
    chat_message: |
      <|start_header_id|>{{if eq .RoleName "assistant"}}assistant{{else if eq .<PERSON>Name "system"}}system{{else if eq .RoleName "tool"}}tool{{else if eq .RoleName "user"}}user{{end}}<|end_header_id|>

      {{ if .FunctionCall -}}
      <tool_call>
      {{ else if eq .<PERSON><PERSON><PERSON> "tool" -}}
      <tool_response>
      {{ end -}}
      {{ if .Content -}}
      {{.Content -}}
      </tool_response>
      {{ else if .FunctionCall -}}
      {{ toJson .FunctionCall -}}
      </tool_call>
      {{ end -}}
      <|eot_id|>
    function: |
      <|start_header_id|>system<|end_header_id|>

      You are a function calling AI model. You are provided with function signatures within <tools></tools> XML tags. You may call one or more functions to assist with the user query. Don't make assumptions about what values to plug into functions.

      Here are the available tools:
      <tools>
      {{range .Functions}}
      {{toJson .}}
      {{end}}
      </tools>

      Use the following pydantic model json schema for each tool call you will make: {"properties": {"arguments": {"title": "Arguments", "type": "object"}, "name": {"title": "Name", "type": "string"}}, "required": ["arguments", "name"], "title": "FunctionCall", "type": "object"}

      For each function call return a json object with function name and arguments within <tool_call></tool_call> XML tags as follows:

      <tool_call>
      {"arguments": <args-dict>, "name": <function-name>}
      </tool_call><|eot_id|>{{.Input }}
      <|start_header_id|>assistant<|end_header_id|>
    chat: |
      {{.Input }}
      <|start_header_id|>assistant<|end_header_id|>
    completion: |
      {{.Input}}

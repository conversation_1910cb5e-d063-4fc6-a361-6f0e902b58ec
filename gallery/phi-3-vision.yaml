---
name: "phi3-vision"

config_file: |
  name: phi3-vision
  backend: vllm
  parameters:
    model: microsoft/Phi-3-vision-128k-instruct
  trust_remote_code: true
  max_model_len: 32768
  template:
    chat_message: |-
        <|{{ .RoleName }}|>
        {{.Content}}<|end|>
    chat: >-
      {{.Input}}

      <|assistant|>

    completion: |
        {{.Input}}
    use_tokenizer_template: false
    multimodal: "{{ range .Images }}<|image_{{ add1 .ID}}|>{{end}}\n{{.Text}}"
    # XXX: The one below can be dropped after a new release is out
    image: "<|image_{{ add1 .ID }}|>\n{{.Text}}"

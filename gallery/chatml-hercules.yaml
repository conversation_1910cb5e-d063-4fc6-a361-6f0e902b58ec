---
name: "chatml-hercules"

config_file: |
  backend: "llama-cpp"
  mmap: true
  function:
    # disable injecting the "answer" tool
    disable_no_action: true

    grammar:
      # This allows the grammar to also return messages
      mixed_mode: true

    return_name_in_function_response: true
    # Without grammar uncomment the lines below
    # Warning: this is relying only on the capability of the
    # LLM model to generate the correct function call.
    json_regex_match:
    - "(?s)<|im_start|>call(.*?)<|im_end|>"
    - "(?s)<|im_start|>call(.*?)"
    replace_function_results:
    # Replace everything that is not JSON array or object
    - key: '(?s)^[^{\[]*'
      value: ""
    - key: '(?s)[^}\]]*$'
      value: ""
    - key: "'([^']*?)'"
      value: "_DQUOTE_${1}_DQUOTE_"
    - key: '\\"'
      value: "__TEMP_QUOTE__"
    - key: "\'"
      value: "'"
    - key: "_DQUOTE_"
      value: '"'
    - key: "__TEMP_QUOTE__"
      value: '"'
  template:
    chat_message: |
      <|im_start|>{{ if .FunctionCall -}}call{{else if eq .<PERSON>Name "tool"}}function{{else}}{{ .RoleName }}{{end}}
      {{ if .Content -}}
      {{.Content }}
      {{ end -}}
      {{ if .FunctionCall -}}
      {{toJson .FunctionCall}}
      {{ end -}}<|im_end|>
    function: |
      <|im_start|>system
      You are a function calling AI model. You are provided with functions to execute. You may call one or more functions to assist with the user query. Don't make assumptions about what values to plug into functions. Here are the available tools:
      {{range .Functions}}
      {'type': 'function', 'function': {'name': '{{.Name}}', 'description': '{{.Description}}', 'parameters': {{toJson .Parameters}} }}
      {{end}}
      For each function call return a json object with function name and arguments
      <|im_end|>
      {{.Input -}}
    chat: |
      {{.Input -}}
      <|im_start|>assistant
    completion: |
      {{.Input}}
  context_size: 4096
  stopwords:
  - '<|im_end|>'
  - '<dummy32000>'
  - '</s>'

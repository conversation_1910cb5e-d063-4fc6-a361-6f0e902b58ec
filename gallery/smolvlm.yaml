---
name: smolvlm
# yamllint disable-line rule:trailing-spaces
config_file: |
    backend: "llama-cpp"
    mmap: true
    template:
      chat_message: |
        {{if eq .<PERSON><PERSON><PERSON> "assistant"}}Assistant{{else if eq .<PERSON><PERSON><PERSON> "system"}}System{{else if eq .<PERSON><PERSON><PERSON> "user"}}User{{end}}: {{.Content }}<end_of_utterance>
      chat: "<|im_start|>\n{{.Input -}}\nAssistant: "
      completion: |
        {{-.Input}}
    f16: true
    stopwords:
    - '<|im_end|>'
    - '<dummy32000>'
    - '</s>'
    - '<|'
    - '<end_of_utterance>'
    - '<|endoftext|>'

---
name: "llama3.2-quantized"

config_file: |
  backend: "llama-cpp"
  mmap: true
  function:
    disable_no_action: true
    grammar:
      disable: true
    response_regex:
    - \[(?P<name>\w+)\((?P<arguments>.*)\)\]
    argument_regex:
    - (?P<key>[^ '\(=,]+)[='"]+(?P<value>[^=,"']+)['"]?
  template:
    chat: |
      <|begin_of_text|><|start_header_id|>system<|end_header_id|>
      You are a helpful assistant<|eot_id|><|start_header_id|>user<|end_header_id|>
      {{.Input }}
      <|start_header_id|>assistant<|end_header_id|>
    chat_message: |
      <|start_header_id|>{{if eq .<PERSON><PERSON>ame "assistant"}}assistant{{else if eq .<PERSON>Name "system"}}system{{else if eq .RoleName "tool"}}tool{{else if eq .<PERSON><PERSON>ame "user"}}user{{end}}<|end_header_id|>
      {{ if .FunctionCall -}}
      {{ else if eq .<PERSON><PERSON><PERSON> "tool" -}}
      The Function was executed and the response was:
      {{ end -}}
      {{ if .Content -}}
      {{.Content -}}
      {{ else if .FunctionCall -}}
      {{ range .FunctionCall }}
      [{{.FunctionCall.Name}}({{.FunctionCall.Arguments}})]
      {{ end }}
      {{ end -}}
      <|eot_id|>
    completion: |
      {{.Input}}
    function: |
      <|start_header_id|>system<|end_header_id|>
      You are an expert in composing functions. You are given a question and a set of possible functions.
      Based on the question, you will need to make one or more function/tool calls to achieve the purpose.
      If none of the functions can be used, point it out. If the given question lacks the parameters required by the function, also point it out. You should only return the function call in tools call sections.
      If you decide to invoke any of the function(s), you MUST put it in the format as follows:
      [func_name1(params_name1=params_value1,params_name2=params_value2,...),func_name2(params_name1=params_value1,params_name2=params_value2,...)]
      You SHOULD NOT include any other text in the response.
      Here is a list of functions in JSON format that you can invoke.
      {{toJson .Functions}}
      <|eot_id|><|start_header_id|>user<|end_header_id|>
      {{.Input}}
      <|eot_id|><|start_header_id|>assistant<|end_header_id|>
  context_size: 8192
  f16: true
  stopwords:
  - <|im_end|>
  - <dummy32000>
  - "<|eot_id|>"
  - <|end_of_text|>

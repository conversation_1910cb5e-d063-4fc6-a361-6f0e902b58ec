---
name: "phi-2-orange"

config_file: |
  backend: "llama-cpp"
  mmap: true
  template:
    chat_message: |
      <|im_start|>{{if eq .<PERSON><PERSON><PERSON> "assistant"}}assistant{{else if eq .<PERSON><PERSON><PERSON> "system"}}system{{else if eq .<PERSON><PERSON><PERSON> "user"}}user{{end}}
      {{if .Content}}{{.Content}}{{end}}<|im_end|>
    chat: |
      {{.Input}}
      <|im_start|>assistant
    completion: |
      {{.Input}}
  context_size: 4096
  f16: true
  stopwords:
  - <|im_end|>
  - <dummy32000>

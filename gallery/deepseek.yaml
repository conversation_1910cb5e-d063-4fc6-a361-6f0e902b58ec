---
name: "deepseek"

config_file: |
  backend: "llama-cpp"
  mmap: true
  context_size: 8192
  template:
    chat_message: |-
      {{if eq .<PERSON><PERSON><PERSON> "user" -}}User: {{.Content }}
      {{ end -}}
      {{if eq .<PERSON><PERSON><PERSON> "assistant" -}}Assistant: {{.Content}}<｜end▁of▁sentence｜>{{end}}
      {{if eq .<PERSON><PERSON>ame "system" -}}{{.Content}}
      {{end -}}
    chat: |
      {{.Input -}}
      Assistant: # Space is preserved for templating reasons, but line does not end with one for the linter.
    completion: |
      {{.Input}}
  stopwords:
  - '<｜end▁of▁sentence｜>'

---
name: "wizardlm2"

config_file: |
  backend: "llama-cpp"
  mmap: true
  template:
    chat_message: |-
      {{if eq .<PERSON><PERSON><PERSON> "assistant"}}ASSISTANT: {{.Content}}</s>{{else if eq .<PERSON><PERSON>ame "system"}}{{.Content}}{{else if eq .<PERSON><PERSON><PERSON> "user"}}USER: {{.Content}}{{end}}
    chat: "{{.Input}}ASSISTANT: "
    completion: |-
      {{.Input}}
  context_size: 32768
  f16: true
  stopwords:
  - </s>

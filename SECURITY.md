# Security Policy

## Introduction

At LocalAI, we take the security of our software seriously. We understand the importance of protecting our community from vulnerabilities and are committed to ensuring the safety and security of our users.

## Supported Versions

We provide support and updates for certain versions of our software. The following table outlines which versions are currently supported with security updates:

| Version | Supported          |
| ------- | ------------------ |
| > 2.0   | :white_check_mark: |
| < 2.0   | :x:                |

Please ensure that you are using a supported version to receive the latest security updates.

## Reporting a Vulnerability

We encourage the responsible disclosure of any security vulnerabilities. If you believe you've found a security issue in our software, we kindly ask you to follow the steps below to report it to us:

1. **Email Us:** Send an email to [<EMAIL>](mailto:<EMAIL>) with a detailed report. Please do not disclose the vulnerability publicly or to any third parties before it has been addressed by us.

2. **Expect a Response:** We aim to acknowledge receipt of vulnerability reports within 48 hours. Our security team will review your report and work closely with you to understand the impact and ensure a thorough investigation.

3. **Collaboration:** If the vulnerability is accepted, we will work with you and our community to address the issue promptly. We'll keep you informed throughout the resolution process and may request additional information or collaboration.

4. **Disclosure:** Once the vulnerability has been resolved, we encourage a coordinated disclosure. We believe in transparency and will work with you to ensure that our community is informed in a responsible manner.

## Use of Third-Party Platforms

As a Free and Open Source Software (FOSS) organization, we do not offer monetary bounties. However, researchers who wish to report vulnerabilities can also do so via [Huntr](https://huntr.dev/bounties), a platform that recognizes contributions to open source security.

## Contact

For any security-related inquiries beyond vulnerability reporting, please contact us at [<EMAIL>](mailto:<EMAIL>).

## Acknowledgments

We appreciate the efforts of those who contribute to the security of our project. Your responsible disclosure is invaluable to the safety and integrity of LocalAI.

Thank you for helping us keep LocalAI secure.

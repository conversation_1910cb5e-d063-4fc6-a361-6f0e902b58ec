baseURL = "https://localai.io/"
languageCode = "en-GB"
contentDir = "content"
enableEmoji = true
enableGitInfo = true # N.B. .GitInfo does not currently function with git submodule content directories

defaultContentLanguage = 'en'


[markup]
  defaultMarkdownHandler = "goldmark"
  [markup.tableOfContents]
      endLevel = 3
      startLevel = 1
  [markup.goldmark]
    [markup.goldmark.renderer]
      unsafe = true # https://jdhao.github.io/2019/12/29/hugo_html_not_shown/
  # [markup.highlight]
  #   codeFences = false # disables <PERSON>'s default syntax highlighting
  # [markup.goldmark.parser]
  #   [markup.goldmark.parser.attribute]
  #     block = true
  #     title = true



[params]

  google_fonts = [
    ["Inter", "300, 400, 600, 700"],
    ["Fira Code", "500, 700"]
  ]

  sans_serif_font = "Inter"     # Default is System font
  secondary_font  = "Inter"     # Default is System font
  mono_font       = "Fira Code" # Default is System font

    [params.footer]
        copyright = "© 2023-2025 <a href='https://mudler.pm' target=_blank>Ettore Di Giacinto</a>"
        version = true # includes git commit info

    [params.social]
        github = "mudler/LocalAI"        # YOUR_GITHUB_ID or YOUR_GITHUB_URL
        twitter = "LocalAI_API"       # YOUR_TWITTER_ID
        dicord = "uJAeKSAGDy"
        # instagram = "colinwilson"     # YOUR_INSTAGRAM_ID
        rss = true                    # show rss icon with link

    [params.docs] # Parameters for the /docs 'template'

        logo = "https://raw.githubusercontent.com/mudler/LocalAI/refs/heads/master/core/http/static/logo.png"
        logo_text = ""
        title           = "LocalAI"           # default html title for documentation pages/sections

        pathName        = "docs"                            # path name for documentation site | default "docs"

        # themeColor      = "cyan"                            # (optional) - Set theme accent colour. Options include: blue (default), green, red, yellow, emerald, cardinal, magenta, cyan

        darkMode        = true                                # enable dark mode option? default false

        prism           = true                                # enable syntax highlighting via Prism

        prismTheme      = "solarized-light"                           # (optional) - Set theme for PrismJS. Options include: lotusdocs (default), solarized-light, twilight, lucario

        # gitinfo
        repoURL         = "https://github.com/mudler/LocalAI"  # Git repository URL for your site [support for GitHub, GitLab, and BitBucket]
        repoBranch      = "master"
        editPage        = true                                # enable 'Edit this page' feature - default false
        lastMod         = true                                # enable 'Last modified' date on pages - default false
        lastModRelative = true                                # format 'Last modified' time as relative - default true

        sidebarIcons    = true                                # enable sidebar icons? default false
        breadcrumbs     = true                                # default is true
        backToTop       = true                                # enable back-to-top button? default true

        # ToC
        toc             = true                                # enable table of contents? default is true
        tocMobile       = true                                # enable table of contents in mobile view? default is true
        scrollSpy       = true                                # enable scrollspy on ToC? default is true

        # front matter
        descriptions    = true                                # enable front matter descriptions under content title?
        titleIcon       = true                                # enable front matter icon title prefix? default is false

        # content navigation
        navDesc         = true                                # include front matter descriptions in Prev/Next navigation cards
        navDescTrunc    = 30                                  # Number of characters by which to truncate the Prev/Next descriptions

        listDescTrunc   = 100                                 # Number of characters by which to truncate the list card description

        # Link behaviour
        intLinkTooltip  = true                                # Enable a tooltip for internal links that displays info about the destination? default false
        # extLinkNewTab   = false                             # Open external links in a new Tab? default true
        # logoLinkURL = ""                                    # Set a custom URL destination for the top header logo link.

    [params.flexsearch] # Parameters for FlexSearch
        enabled             = true
        # tokenize            = "full"
        # optimize            = true
        # cache               = 100
        # minQueryChar        = 3 # default is 0 (disabled)
        # maxResult           = 5 # default is 5
        # searchSectionsIndex = []

    [params.docsearch] # Parameters for DocSearch
        # appID     = "" # Algolia Application ID
        # apiKey    = "" # Algolia Search-Only API (Public) Key
        # indexName = "" # Index Name to perform search on (or set env variable HUGO_PARAM_DOCSEARCH_indexName)

    [params.analytics] # Parameters for Analytics (Google, Plausible)
        # google = "G-XXXXXXXXXX" # Replace with your Google Analytics ID
        # plausibleURL    = "/docs/s" # (or set via env variable HUGO_PARAM_ANALYTICS_plausibleURL)
        # plausibleAPI    = "/docs/s" # optional - (or set via env variable HUGO_PARAM_ANALYTICS_plausibleAPI)
        # plausibleDomain = ""      # (or set via env variable HUGO_PARAM_ANALYTICS_plausibleDomain)

    # [params.feedback]
    #     enabled = true
    #     emoticonTpl = true
    #     eventDest = ["plausible","google"]
    #     emoticonEventName = "Feedback"
    #     positiveEventName = "Positive Feedback"
    #     negativeEventName = "Negative Feedback"
    #     positiveFormTitle = "What did you like?"
    #     negativeFormTitle = "What went wrong?"
    #     successMsg = "Thank you for helping to improve Lotus Docs' documentation!"
    #     errorMsg = "Sorry! There was an error while attempting to submit your feedback!"
    #     positiveForm = [
    #       ["Accurate", "Accurately describes the feature or option."],
    #       ["Solved my problem", "Helped me resolve an issue."],
    #       ["Easy to understand", "Easy to follow and comprehend."],
    #       ["Something else"]
    #     ]
    #     negativeForm = [
    #       ["Inaccurate", "Doesn't accurately describe the feature or option."],
    #       ["Couldn't find what I was looking for", "Missing important information."],
    #       ["Hard to understand", "Too complicated or unclear."],
    #       ["Code sample errors", "One or more code samples are incorrect."],
    #       ["Something else"]
    #     ]

[menu]
 [[menu.primary]]
    name  = "Docs"
    url = "docs/"
    identifier = "docs"
    weight = 10
[[menu.primary]]
    name = "Discord"
    url = "https://discord.gg/uJAeKSAGDy"
    identifier = "discord"
    weight = 20

[languages]
  [languages.en]
    title = "LocalAI"
    languageName = "English"
    weight = 10
#  [languages.fr]
#    title = "LocalAI documentation"
#    languageName = "Français"
#    contentDir = "content/fr"
#    weight = 20
#  [languages.de]
#    title = "LocalAI documentation"
#    languageName = "Deutsch"
#    contentDir = "content/de"
#    weight = 30





# mounts are only needed in this showcase to access the publicly available screenshots;
# remove this section if you don't need further mounts
[module]
  replacements = "github.com/colinwilson/lotusdocs -> lotusdocs"
  [[module.mounts]]
    source = 'archetypes'
    target = 'archetypes'
  [[module.mounts]]
    source = 'assets'
    target = 'assets'
  [[module.mounts]]
    source = 'content'
    target = 'content'
  [[module.mounts]]
    source = 'data'
    target = 'data'
  [[module.mounts]]
    source = 'i18n'
    target = 'i18n'
  [[module.mounts]]
    source = '../images'
    target = 'static/images'
  [[module.mounts]]
    source = 'layouts'
    target = 'layouts'
  [[module.mounts]]
    source = 'static'
    target = 'static'
    # uncomment line below for temporary local development of module
    # or when using a 'theme' as a git submodule
  [[module.imports]]
    path = "github.com/colinwilson/lotusdocs"
    disable = false
  [[module.imports]]
    path = "github.com/gohugoio/hugo-mod-bootstrap-scss/v5"
    disable = false
